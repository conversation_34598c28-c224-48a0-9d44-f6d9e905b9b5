import React from "react"
import {
  <PERSON><PERSON>ian<PERSON>rid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample data for market share growth by segment
const marketShareData = [
  {
    year: "2018",
    aesthetics: 8.5,
    corporate: 12.3,
    imaging: 15.2,
    oncology: 6.8,
    paediatrics: 4.2,
    womensHealth: 9.1,
    others: 3.5,
  },
  {
    year: "2019",
    aesthetics: 9.2,
    corporate: 13.1,
    imaging: 16.8,
    oncology: 7.5,
    paediatrics: 4.8,
    womensHealth: 10.2,
    others: 4.1,
  },
  {
    year: "2020",
    aesthetics: 10.8,
    corporate: 14.5,
    imaging: 18.2,
    oncology: 8.9,
    paediatrics: 5.5,
    womensHealth: 11.8,
    others: 4.9,
  },
  {
    year: "2021",
    aesthetics: 12.4,
    corporate: 15.8,
    imaging: 19.5,
    oncology: 10.2,
    paediatrics: 6.3,
    womensHealth: 13.1,
    others: 5.8,
  },
  {
    year: "2022",
    aesthetics: 14.1,
    corporate: 17.2,
    imaging: 21.3,
    oncology: 11.8,
    paediatrics: 7.2,
    womensHealth: 14.9,
    others: 6.7,
  },
  {
    year: "2023",
    aesthetics: 16.2,
    corporate: 18.9,
    imaging: 23.1,
    oncology: 13.5,
    paediatrics: 8.1,
    womensHealth: 16.8,
    others: 7.9,
  },
  {
    year: "2024",
    aesthetics: 18.5,
    corporate: 20.8,
    imaging: 25.2,
    oncology: 15.4,
    paediatrics: 9.2,
    womensHealth: 18.9,
    others: 9.1,
  },
]

interface MarketShareGrowthChartProps {
  data?: typeof marketShareData
}

const MarketShareGrowthChart: React.FC<MarketShareGrowthChartProps> = ({
  data = marketShareData,
}) => {
  // Ensure data is an array
  const chartData = Array.isArray(data) ? data : marketShareData

  // Define segment colors
  const segmentColors = {
    aesthetics: "#8884d8",
    corporate: "#82ca9d",
    imaging: "#ffc658",
    oncology: "#ff7300",
    paediatrics: "#8dd1e1",
    womensHealth: "#d084d0",
    others: "#87d068",
  }

  // Calculate metrics for the leading segment (imaging)
  const currentData = chartData[chartData.length - 1]
  const previousData = chartData[chartData.length - 2]
  const initialData = chartData[0]

  // Find the leading segment
  const segments = [
    "aesthetics",
    "corporate",
    "imaging",
    "oncology",
    "paediatrics",
    "womensHealth",
    "others",
  ] as const
  const leadingSegment = segments.reduce((prev, current) =>
    currentData[current] > currentData[prev] ? current : prev
  )

  const currentMarketShare = currentData[leadingSegment]
  const previousMarketShare = previousData[leadingSegment]
  const initialMarketShare = initialData[leadingSegment]

  const yearlyGrowth = currentMarketShare - previousMarketShare
  const yearlyGrowthPercent = Math.round(
    (yearlyGrowth / previousMarketShare) * 100
  )

  const totalGrowth = currentMarketShare - initialMarketShare
  const totalGrowthPercent = Math.round(
    (totalGrowth / initialMarketShare) * 100
  )

  // Calculate CAGR (Compound Annual Growth Rate) for leading segment
  const years = chartData.length - 1
  const cagr = Math.round(
    (Math.pow(currentMarketShare / initialMarketShare, 1 / years) - 1) * 100
  )

  // Calculate average growth across all segments
  const avgGrowth =
    segments.reduce((sum, segment) => {
      const segmentGrowth =
        ((currentData[segment] - initialData[segment]) / initialData[segment]) *
        100
      return sum + segmentGrowth
    }, 0) / segments.length

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <div className="rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
            Leading:{" "}
            {leadingSegment.charAt(0).toUpperCase() + leadingSegment.slice(1)} (
            {currentMarketShare}%)
          </div>
          <div className="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            YoY: +{yearlyGrowthPercent}%
          </div>
        </div>
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <LineChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="year" tick={{ fontSize: 12 }} />
          <YAxis
            domain={[0, "dataMax + 2"]}
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `${value}%`}
          />
          <Tooltip formatter={(value, name) => [`${value}%`, name]} />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Line
            type="monotone"
            dataKey="aesthetics"
            name="Aesthetics"
            stroke={segmentColors.aesthetics}
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="corporate"
            name="Corporate"
            stroke={segmentColors.corporate}
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="imaging"
            name="Imaging"
            stroke={segmentColors.imaging}
            strokeWidth={3}
          />
          <Line
            type="monotone"
            dataKey="oncology"
            name="Oncology"
            stroke={segmentColors.oncology}
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="paediatrics"
            name="Paediatrics"
            stroke={segmentColors.paediatrics}
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="womensHealth"
            name="Women's Health"
            stroke={segmentColors.womensHealth}
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="others"
            name="Others"
            stroke={segmentColors.others}
            strokeWidth={2}
          />
        </LineChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2">
        <div className="rounded bg-blue-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-700">
            Leading Segment Growth
          </p>
          <p className="text-lg font-bold text-blue-600">
            +{totalGrowthPercent}%
          </p>
        </div>
        <div className="rounded bg-purple-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-700">CAGR (Leading)</p>
          <p className="text-lg font-bold text-purple-600">{cagr}%</p>
        </div>
        <div className="rounded bg-green-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-700">Avg Growth</p>
          <p className="text-lg font-bold text-green-600">
            +{Math.round(avgGrowth)}%
          </p>
        </div>
      </div>
    </div>
  )
}

export default MarketShareGrowthChart
