import React from "react"
import {
  <PERSON>,
  CartesianGrid,
  Composed<PERSON>hart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YA<PERSON>s,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample data for staff growth
const staffGrowthData = [
  {
    quarter: "Q1 2023",
    physicians: 45,
    nurses: 120,
    support: 85,
    admin: 30,
    turnoverRate: 8.2,
    hiringTarget: 270,
    revenueGrowth: 12.5,
    profitGrowth: 8.3,
  },
  {
    quarter: "Q2 2023",
    physicians: 48,
    nurses: 128,
    support: 90,
    admin: 32,
    turnoverRate: 7.8,
    hiringTarget: 280,
    revenueGrowth: 15.2,
    profitGrowth: 11.7,
  },
  {
    quarter: "Q3 2023",
    physicians: 52,
    nurses: 135,
    support: 95,
    admin: 35,
    turnoverRate: 7.5,
    hiringTarget: 290,
    revenueGrowth: 18.1,
    profitGrowth: 14.2,
  },
  {
    quarter: "Q4 2023",
    physicians: 55,
    nurses: 142,
    support: 98,
    admin: 38,
    turnoverRate: 7.2,
    hiringTarget: 300,
    revenueGrowth: 21.3,
    profitGrowth: 16.8,
  },
  {
    quarter: "Q1 2024",
    physicians: 58,
    nurses: 150,
    support: 105,
    admin: 40,
    turnoverRate: 6.8,
    hiringTarget: 310,
    revenueGrowth: 24.7,
    profitGrowth: 19.5,
  },
  {
    quarter: "Q2 2024",
    physicians: 62,
    nurses: 158,
    support: 110,
    admin: 42,
    turnoverRate: 6.5,
    hiringTarget: 320,
    revenueGrowth: 27.9,
    profitGrowth: 22.1,
  },
  {
    quarter: "Q3 2024",
    physicians: 65,
    nurses: 165,
    support: 115,
    admin: 45,
    turnoverRate: 6.2,
    hiringTarget: 330,
    revenueGrowth: 30.4,
    profitGrowth: 24.8,
  },
]

interface StaffGrowthChartProps {
  data?: typeof staffGrowthData
}

const StaffGrowthChart: React.FC<StaffGrowthChartProps> = ({
  data = staffGrowthData,
}) => {
  // Ensure data is an array
  const chartData = Array.isArray(data) ? data : staffGrowthData

  // Calculate metrics
  const currentQuarter = chartData[chartData.length - 1]
  const previousQuarter = chartData[chartData.length - 2]

  const currentTotal =
    currentQuarter.physicians +
    currentQuarter.nurses +
    currentQuarter.support +
    currentQuarter.admin

  const previousTotal =
    previousQuarter.physicians +
    previousQuarter.nurses +
    previousQuarter.support +
    previousQuarter.admin

  const staffGrowth = currentTotal - previousTotal
  const staffGrowthPercent = Math.round((staffGrowth / previousTotal) * 100)

  // Calculate physician to nurse ratio
  const physicianToNurseRatio =
    Math.round((currentQuarter.nurses / currentQuarter.physicians) * 10) / 10

  // Calculate if target was met
  const targetMet = currentTotal >= currentQuarter.hiringTarget
  const targetDiff = Math.abs(currentTotal - currentQuarter.hiringTarget)
  const targetDiffPercent = Math.round(
    (targetDiff / currentQuarter.hiringTarget) * 100
  )

  // Calculate staff composition percentages
  const physicianPercent = Math.round(
    (currentQuarter.physicians / currentTotal) * 100
  )
  const nursePercent = Math.round((currentQuarter.nurses / currentTotal) * 100)
  const supportPercent = Math.round(
    (currentQuarter.support / currentTotal) * 100
  )
  const adminPercent = Math.round((currentQuarter.admin / currentTotal) * 100)

  // Colors for the bars
  const barColors = ["#8884d8", "#82ca9d", "#ffc658", "#ff7300"]

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <div className="rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
            Total Staff: {currentTotal}
          </div>
          <div className="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            Growth: +{staffGrowthPercent}%
          </div>
        </div>
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ComposedChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="quarter" tick={{ fontSize: 12 }} />
          <YAxis yAxisId="left" orientation="left" tick={{ fontSize: 12 }} />
          <YAxis
            yAxisId="right"
            orientation="right"
            domain={[0, 10]}
            tickFormatter={(value) => `${value}%`}
            tick={{ fontSize: 12 }}
          />
          <Tooltip
            formatter={(value, name) => {
              if (
                name === "Turnover Rate" ||
                name === "Revenue Growth" ||
                name === "Profit Growth"
              )
                return [`${value}%`, name]
              return [value, name]
            }}
          />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Bar
            dataKey="physicians"
            name="Physicians"
            stackId="a"
            fill={barColors[0]}
            yAxisId="left"
          />
          <Bar
            dataKey="nurses"
            name="Nurses"
            stackId="a"
            fill={barColors[1]}
            yAxisId="left"
          />
          <Bar
            dataKey="support"
            name="Support Staff"
            stackId="a"
            fill={barColors[2]}
            yAxisId="left"
          />
          <Bar
            dataKey="admin"
            name="Admin Staff"
            stackId="a"
            fill={barColors[3]}
            yAxisId="left"
          />
          <Line
            type="monotone"
            dataKey="turnoverRate"
            name="Turnover Rate"
            stroke="#ff0000"
            yAxisId="right"
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="hiringTarget"
            name="Hiring Target"
            stroke="#000000"
            yAxisId="left"
            strokeWidth={2}
            strokeDasharray="5 5"
          />
          <Line
            type="monotone"
            dataKey="revenueGrowth"
            name="Revenue Growth"
            stroke="#00aa00"
            yAxisId="right"
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="profitGrowth"
            name="Profit Growth"
            stroke="#0066cc"
            yAxisId="right"
            strokeWidth={2}
          />
        </ComposedChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-4 gap-2">
        <div className="rounded bg-indigo-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Physicians</p>
          <p className="text-lg font-bold text-indigo-600">
            {physicianPercent}%
          </p>
          <p className="text-xs text-indigo-600">{currentQuarter.physicians}</p>
        </div>
        <div className="rounded bg-green-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Nurses</p>
          <p className="text-lg font-bold text-green-600">{nursePercent}%</p>
          <p className="text-xs text-green-600">{currentQuarter.nurses}</p>
        </div>
        <div className="rounded bg-yellow-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Support</p>
          <p className="text-lg font-bold text-yellow-600">{supportPercent}%</p>
          <p className="text-xs text-yellow-600">{currentQuarter.support}</p>
        </div>
        <div className="rounded bg-orange-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Admin</p>
          <p className="text-lg font-bold text-orange-600">{adminPercent}%</p>
          <p className="text-xs text-orange-600">{currentQuarter.admin}</p>
        </div>
      </div>

      <div className="-mt-2 grid grid-cols-2 gap-2">
        <div className="rounded bg-blue-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">
            Nurse to Physician Ratio
          </p>
          <p className="text-lg font-bold text-blue-600">
            {physicianToNurseRatio}:1
          </p>
        </div>
        <div
          className={`${targetMet ? "bg-green-50" : "bg-red-50"} rounded p-2 text-center`}
        >
          <p className="text-xs font-medium text-gray-500">Hiring Target</p>
          <p
            className={`text-lg font-bold ${targetMet ? "text-green-600" : "text-red-600"}`}
          >
            {targetMet ? "Met" : `${targetDiffPercent}% Below`}
          </p>
        </div>
      </div>
    </div>
  )
}

export default StaffGrowthChart
