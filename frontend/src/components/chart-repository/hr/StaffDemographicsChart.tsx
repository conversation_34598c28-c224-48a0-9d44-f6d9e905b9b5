import React from "react"
import {
  <PERSON>,
  Car<PERSON>ianGrid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample years of service distribution by occupation
const sampleServiceDistribution = [
  // Doctor years of service distribution (typically longer tenure)
  { occupation: "Doctor", yearsOfService: 1, count: 8 },
  { occupation: "Doctor", yearsOfService: 2, count: 12 },
  { occupation: "Doctor", yearsOfService: 3, count: 15 },
  { occupation: "Doctor", yearsOfService: 4, count: 18 },
  { occupation: "Doctor", yearsOfService: 5, count: 16 },
  { occupation: "Doctor", yearsOfService: 6, count: 14 },
  { occupation: "Doctor", yearsOfService: 7, count: 10 },
  { occupation: "Doctor", yearsOfService: 8, count: 8 },
  { occupation: "Doctor", yearsOfService: 10, count: 6 },
  { occupation: "Doctor", yearsOfService: 12, count: 4 },
  { occupation: "Doctor", yearsOfService: 15, count: 3 },
  { occupation: "Doctor", yearsOfService: 20, count: 2 },

  // Clinic Assistant years of service distribution (shorter tenure, higher turnover)
  { occupation: "Clinic Assistant", yearsOfService: 0.5, count: 15 },
  { occupation: "Clinic Assistant", yearsOfService: 1, count: 25 },
  { occupation: "Clinic Assistant", yearsOfService: 2, count: 22 },
  { occupation: "Clinic Assistant", yearsOfService: 3, count: 18 },
  { occupation: "Clinic Assistant", yearsOfService: 4, count: 15 },
  { occupation: "Clinic Assistant", yearsOfService: 5, count: 12 },
  { occupation: "Clinic Assistant", yearsOfService: 6, count: 8 },
  { occupation: "Clinic Assistant", yearsOfService: 7, count: 6 },
  { occupation: "Clinic Assistant", yearsOfService: 8, count: 4 },
  { occupation: "Clinic Assistant", yearsOfService: 10, count: 3 },
  { occupation: "Clinic Assistant", yearsOfService: 12, count: 2 },

  // Staff Nurse years of service distribution (moderate tenure)
  { occupation: "Staff Nurse", yearsOfService: 0.5, count: 8 },
  { occupation: "Staff Nurse", yearsOfService: 1, count: 14 },
  { occupation: "Staff Nurse", yearsOfService: 2, count: 18 },
  { occupation: "Staff Nurse", yearsOfService: 3, count: 20 },
  { occupation: "Staff Nurse", yearsOfService: 4, count: 18 },
  { occupation: "Staff Nurse", yearsOfService: 5, count: 15 },
  { occupation: "Staff Nurse", yearsOfService: 6, count: 12 },
  { occupation: "Staff Nurse", yearsOfService: 7, count: 9 },
  { occupation: "Staff Nurse", yearsOfService: 8, count: 6 },
  { occupation: "Staff Nurse", yearsOfService: 10, count: 4 },
  { occupation: "Staff Nurse", yearsOfService: 12, count: 3 },
  { occupation: "Staff Nurse", yearsOfService: 15, count: 2 },

  // Radiographer years of service distribution
  { occupation: "Radiographer", yearsOfService: 1, count: 4 },
  { occupation: "Radiographer", yearsOfService: 2, count: 6 },
  { occupation: "Radiographer", yearsOfService: 3, count: 8 },
  { occupation: "Radiographer", yearsOfService: 4, count: 7 },
  { occupation: "Radiographer", yearsOfService: 5, count: 5 },
  { occupation: "Radiographer", yearsOfService: 6, count: 3 },
  { occupation: "Radiographer", yearsOfService: 8, count: 2 },

  // Patient Relations Consultant years of service distribution
  { occupation: "Patient Relations Consultant", yearsOfService: 0.5, count: 5 },
  { occupation: "Patient Relations Consultant", yearsOfService: 1, count: 7 },
  { occupation: "Patient Relations Consultant", yearsOfService: 2, count: 6 },
  { occupation: "Patient Relations Consultant", yearsOfService: 3, count: 4 },
  { occupation: "Patient Relations Consultant", yearsOfService: 4, count: 3 },
  { occupation: "Patient Relations Consultant", yearsOfService: 5, count: 3 },

  // Dental Assistant years of service distribution
  { occupation: "Dental Assistant", yearsOfService: 0.5, count: 5 },
  { occupation: "Dental Assistant", yearsOfService: 1, count: 6 },
  { occupation: "Dental Assistant", yearsOfService: 2, count: 4 },
  { occupation: "Dental Assistant", yearsOfService: 3, count: 3 },
  { occupation: "Dental Assistant", yearsOfService: 4, count: 2 },
  { occupation: "Dental Assistant", yearsOfService: 5, count: 2 },
]

// Sample staff count by occupation
const sampleStaffByOccupation = [
  { occupation: "Doctor", count: 104 },
  { occupation: "Clinic Assistant", count: 125 },
  { occupation: "Staff Nurse", count: 115 },
  { occupation: "Radiographer", count: 35 },
  { occupation: "Patient Relations Consultant", count: 28 },
  { occupation: "Dental Assistant", count: 22 },
  { occupation: "Executive", count: 18 },
  { occupation: "Manager", count: 15 },
  { occupation: "Sonographer", count: 12 },
  { occupation: "Healthcare Assistant", count: 10 },
]

// Create chart data by transforming the years of service distribution into service groups
const createChartData = () => {
  // Define years of service groups
  const serviceGroups = [
    { range: "0-1", min: 0, max: 1 },
    { range: "2-3", min: 2, max: 3 },
    { range: "4-5", min: 4, max: 5 },
    { range: "6-7", min: 6, max: 7 },
    { range: "8-10", min: 8, max: 10 },
    { range: "11-15", min: 11, max: 15 },
    { range: "16+", min: 16, max: 100 },
  ]

  // Get top 6 occupations for better visualization
  const topOccupations = [
    "Doctor",
    "Clinic Assistant",
    "Staff Nurse",
    "Radiographer",
    "Patient Relations Consultant",
    "Dental Assistant",
  ]

  const chartData = serviceGroups.map((serviceGroup) => {
    const dataPoint: any = { serviceGroup: serviceGroup.range }

    topOccupations.forEach((occupation) => {
      const occupationData = sampleServiceDistribution.filter(
        (item) =>
          item.occupation === occupation &&
          item.yearsOfService >= serviceGroup.min &&
          item.yearsOfService <= serviceGroup.max
      )

      const totalCount = occupationData.reduce(
        (sum, item) => sum + item.count,
        0
      )
      dataPoint[occupation] = totalCount
    })

    return dataPoint
  })

  return chartData
}

const chartData = createChartData()

interface StaffDemographicsChartProps {
  data?: typeof chartData
}

const StaffDemographicsChart: React.FC<StaffDemographicsChartProps> = ({
  data = chartData,
}) => {
  // Calculate overall metrics
  const totalStaff = sampleStaffByOccupation.reduce(
    (sum, item) => sum + item.count,
    0
  )
  const topSixOccupations = [
    "Doctor",
    "Clinic Assistant",
    "Staff Nurse",
    "Radiographer",
    "Patient Relations Consultant",
    "Dental Assistant",
  ]

  const topSixCount = topSixOccupations.reduce((sum, occupation) => {
    const count =
      sampleStaffByOccupation.find((item) => item.occupation === occupation)
        ?.count || 0
    return sum + count
  }, 0)

  const topSixPercent = Math.round((topSixCount / totalStaff) * 100)

  // Find most common service group
  const serviceGroupCounts = data.map((group) => ({
    serviceGroup: group.serviceGroup,
    total: topSixOccupations.reduce((sum, occ) => sum + (group[occ] || 0), 0),
  }))
  const mostCommonServiceGroup = serviceGroupCounts.reduce((max, current) =>
    current.total > max.total ? current : max
  )

  return (
    <div className="flex flex-col gap-4">
      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ComposedChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="serviceGroup" tick={{ fontSize: 12 }} />
          <YAxis
            label={{
              value: "Number of Staff",
              angle: -90,
              position: "insideLeft",
              fontSize: 12,
            }}
            tick={{ fontSize: 12 }}
          />
          <Tooltip
            formatter={(value, name) => [value, name]}
            labelFormatter={(label) => `Years of Service: ${label}`}
          />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Bar dataKey="Doctor" name="Doctor" stackId="staff" fill="#8884d8" />
          <Bar
            dataKey="Clinic Assistant"
            name="Clinic Assistant"
            stackId="staff"
            fill="#82ca9d"
          />
          <Bar
            dataKey="Staff Nurse"
            name="Staff Nurse"
            stackId="staff"
            fill="#ff7c7c"
          />
          <Bar
            dataKey="Radiographer"
            name="Radiographer"
            stackId="staff"
            fill="#ffc658"
          />
          <Bar
            dataKey="Patient Relations Consultant"
            name="Patient Relations Consultant"
            stackId="staff"
            fill="#8dd1e1"
          />
          <Bar
            dataKey="Dental Assistant"
            name="Dental Assistant"
            stackId="staff"
            fill="#d084d0"
          />
        </ComposedChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2 text-center">
        <div className="rounded bg-blue-50 p-2">
          <p className="text-xs font-medium text-gray-500">Total Staff</p>
          <p className="text-lg font-bold text-blue-600">{totalStaff}</p>
        </div>
        <div className="rounded bg-green-50 p-2">
          <p className="text-xs font-medium text-gray-500">Top 6 Roles</p>
          <p className="text-lg font-bold text-green-600">
            {topSixPercent}% of staff
          </p>
        </div>
        <div className="rounded bg-purple-50 p-2">
          <p className="text-xs font-medium text-gray-500">
            Peak Service Group
          </p>
          <p className="text-lg font-bold text-purple-600">
            {mostCommonServiceGroup.serviceGroup} years
          </p>
        </div>
      </div>
    </div>
  )
}

export default StaffDemographicsChart
